package com.birdeye.social.entities.report;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "social_ai_non_useful_posts")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialAINonUsefulPosts {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "account_id", nullable = false)
    private Integer accountId;

    @Column(name = "external_post_id")
    private String externalPostId;

    @Column(name = "created_at", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

}