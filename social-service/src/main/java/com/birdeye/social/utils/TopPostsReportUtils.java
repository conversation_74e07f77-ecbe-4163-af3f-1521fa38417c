package com.birdeye.social.utils;

import com.birdeye.social.insights.Facebook.PagePostData;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.birdeye.social.insights.constants.InsightsConstants.*;

public class TopPostsReportUtils {
    public static List<List<Integer>> splitListIntoBatches(List<Integer> pageIds, int batchSize) {
        List<List<Integer>> batches = new ArrayList<>();
        for (int i = 0; i < pageIds.size(); i += batchSize) {
            batches.add(pageIds.subList(i, Math.min(i + batchSize, pageIds.size())));
        }
        return batches;
    }

    public static PostDataAndInsightResponse getSortedInsightResponse(List<PostDataAndInsightResponse> postDataAndInsightResponses, int pageSize, String sortParam, String sortOrder) {
        PostDataAndInsightResponse response = new PostDataAndInsightResponse();
        List<PagePostData> pagePostDataList = new ArrayList<>();
        postDataAndInsightResponses.forEach(element -> pagePostDataList.addAll(element.getPagePostData()));
        Comparator<PagePostData> comparator = getPagePostDataComparator(sortParam, sortOrder);
        response.setPagePostData(pagePostDataList.stream().sorted(comparator).limit(pageSize).collect(Collectors.toList()));
        return response;
    }

    @NotNull
    private static Comparator<PagePostData> getPagePostDataComparator(String sortParam, String sortOrder) {
        Comparator<PagePostData> comparator;

        switch (sortParam) {
            case REACH:
                comparator = (e1, e2) -> Integer.compare(e2.getReach(), e1.getReach());
                break;
            case IMPRESSIONS:
                comparator = (e1, e2) -> Integer.compare(e2.getImpression(), e1.getImpression());
                break;
            case LIKE_COUNT:
                 comparator = (e1, e2) -> Integer.compare(e2.getLikeCount(), e1.getLikeCount());
                 break;
            case COMMENT_COUNT:
                comparator = (e1, e2) -> Integer.compare(e2.getCommentCount(), e1.getCommentCount());
                break;
            case SHARE_COUNT:
                comparator = (e1, e2) -> Integer.compare(e2.getShareCount(), e1.getShareCount());
                break;
            default:
                comparator = (e1, e2) -> Integer.compare(e2.getEngagement(), e1.getEngagement());
                break;
        }
        if ("asc".equalsIgnoreCase(sortOrder)) {
            comparator = comparator.reversed();
        }
        return comparator;
    }
}
