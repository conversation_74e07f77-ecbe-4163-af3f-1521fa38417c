package com.birdeye.social.service;

import com.birdeye.social.constant.FilterPostType;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.GlobalFilterCriteriaSchedulePostMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class SocialPostCalendarValidationService {

    private static final Logger LOG = LoggerFactory.getLogger(SocialPostCalendarValidationService.class);


    public boolean isListViewRequestValid(GlobalFilterCriteriaSchedulePostMessage filter){
        if(Objects.isNull(filter.getPageNo()) || Objects.isNull(filter.getPageSize()) || Objects.isNull(filter.getOrder())) {
            LOG.info("Social Post: pageNo, or pageSize or order is missing for page view");
            throw new BirdeyeSocialException(ErrorCodes.CLIENT_ERROR_400, "pageNo, or pageSize or order is missing for page view:");
        }
        if(CollectionUtils.isNotEmpty(filter.getPostType()) && filter.getPostType().size()==1
                && filter.getPostType().contains(FilterPostType.AI_SUGGESTED_POSTS)){
            LOG.info("Social Post: AI Suggested Posts is not allowed for page view");
            return false;
        }
        return true;
    }
}
