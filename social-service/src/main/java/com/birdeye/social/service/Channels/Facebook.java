package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.reports.FacebookPageInsightRepo;
import com.birdeye.social.dto.PicturesqueMediaCallback;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.PageInsightV2EsData;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetails;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetailsDTO;
import com.birdeye.social.model.competitorProfile.CompetitorProfileReportESData;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.service.FacebookSocialAccountService;
import com.birdeye.social.service.SocialCompetitorService.Facebook.FacebookCompetitorService;
import com.birdeye.social.service.SocialCompetitorService.SocialCompetitor;
import com.birdeye.social.service.SocialPostOperationService.Facebook.FacebookPostOperationService;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialReportService.Facebook.FacebookInsights;
import com.birdeye.social.service.SocialReportService.Facebook.FacebookReportService;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.service.notification.NotificationAuditService;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class Facebook implements SocialInsights, NotificationAuditService, SocialCompetitor, PostOperation {

    @Autowired
    private FacebookInsights facebookInsights;

    @Autowired
    private FacebookReportService facebookPageService;

    @Autowired
    private FacebookSocialAccountService fbSocialAccountService;

    @Autowired
    private FacebookPageInsightRepo facebookPageInsightRepo;

    @Autowired
    private SocialFBPageRepository socialFBPageRepository;


    @Autowired
    KafkaProducerService kafkaProducerService;

    @Autowired
    private FacebookCompetitorService facebookCompetitorService;

    @Autowired
    private FacebookPostOperationService postOperationService;

    public static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss+SSSS";

    private static final Logger log = LoggerFactory.getLogger(Facebook.class);


    @Override
    public String channelName() {
        return SocialChannel.FACEBOOK.getName();
    }

    @Override
    public List<SocialNotificationAudit> putNotificationForChannel(Object notificationObject) {
        return fbSocialAccountService.auditNotifications(notificationObject);
    }

    @Override
    public Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.nonNull(searchTemplate)) {
            return facebookInsights.getFacebookInsightsForPage(insightsRequest);
        }
        return null;
    }

    @Override
    public Object getPageInsightsESData(InsightsRequest insightsRequest) throws Exception {
        log.info("[getPageInsightsESData] Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.equals(searchTemplate)){
            PageInsightV2EsData engageData = facebookInsights.getFacebookInsightsForMessageSent(insightsRequest);

            PageInsightV2EsData postData = facebookInsights.getFacebookInsightsForPublishPost(insightsRequest, SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC);
            if(Objects.nonNull(postData)) {
                engageData.setBuckets(postData.getBuckets());
                engageData.setCurrentData(postData.getCurrentData());
            }
            insightsRequest.setReportType(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.getName());
            return engageData;
        } else if(SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC.equals(searchTemplate)) {
            return facebookInsights.getFacebookInsightsForPublishPost(insightsRequest, SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC);
        } else if(SearchTemplate.FB_POST_PUBLISHING_BEHAVIOUR.equals(searchTemplate)) {
            return facebookInsights.getFacebookInsightsForPublishPost(insightsRequest, SearchTemplate.FB_POST_PUBLISHING_BEHAVIOUR);
        } else {
            return facebookInsights.getFacebookInsightsESData(insightsRequest);
        }
    }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }

        return facebookInsights.getFacebookInsightsForPageByLocation(insightsRequest);
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {
        if(Objects.isNull(pageInsights)){
            return;
        }
        facebookInsights.postFacebookPageInsightToES(pageInsights);
    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO) {
        facebookPageService.getPageInsightsFromFacebook(socialScanEventDTO);
    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {
        facebookInsights.postFacebookPostInsightsToEs(postData);
    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder,
                                               boolean excelDownload) {
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.isNull(searchTemplate)){
            return null;
        }
        return facebookInsights.getFacebookInsightsForPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) throws IOException {
        if(Objects.isNull(newFbPostData)){
            return;
        }
        BusinessPosts businessPosts = facebookPageService.updatePostToDb(newFbPostData);
        if(Objects.isNull(businessPosts)){
            return;
        }
        facebookInsights.updateToPostAndPageIndexEs(newFbPostData,businessPosts);

    }

    @Override
    public void updatePageInsightsDb(String pageId, Integer businessId, Long enterpriseId) {
        if(StringUtils.isEmpty(pageId)){
            return;
        }
        facebookPageInsightRepo.updateBusinessIdWherePageId(pageId,businessId,enterpriseId);
    }

    @Override
    public void updatePageInsightsPostCount(PageInsights pageInsights) {

    }

    @Override
    public String getPageInsightIndexName() {
        return ElasticConstants.FACEBOOK_PAGE_INSIGHTS.getName();
    }

    @Override
    public void startScanForPosts(String pageId) {

    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {
        if(CollectionUtils.isEmpty(businessPosts)){
            return;
        }
        facebookInsights.getPostInsights(businessPosts.get(0),isFreshRequest);
    }


	@Override
	public void getGMBPageAnalytics(String pageId, Integer businessId) {
		// TODO Auto-generated method stub

	}


    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {
        if(Objects.isNull(businessPosts)){
            return;
        }
        facebookInsights.saveCDNPostToES(businessPosts);
    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {
        // TODO Auto-generated method stub

    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest)  {
       return facebookInsights.getFacebookPerformanceData(insightsRequest);
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return facebookInsights.backfillProfilePagesToEs(pageInsights);
    }


    @Override
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.REPORT_MESSAGE_VOLUME.equals(searchTemplate)){
            return facebookInsights.getMessageVolumeInsightsReportData(insightsRequest);
        } else {
            return facebookInsights.getFacebookInsightsReportData(insightsRequest);
        }
    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        List<BusinessFBPage> fbPages = socialFBPageRepository.findAllByBusinessIdIn(businessIds);
        if(CollectionUtils.isNotEmpty(fbPages)){
            Map<String, Integer> businessIdPageIdMap = new HashMap<>();
            fbPages.forEach(fbPage -> {
                businessIdPageIdMap.put(fbPage.getFacebookPageId(), fbPage.getBusinessId());
            });
            return businessIdPageIdMap;
        }
        return null;
    }

    @Override
    public void fetchCompetitorPosts(CompetitorRequestDTO competitorRequestDTO) {
        if(Objects.nonNull(competitorRequestDTO.getPageId())) {
            facebookCompetitorService.fetchCompetitorPosts(competitorRequestDTO);
        } else {
            log.info("[FB competitor posts] pageId cannot be null in request, exiting the flow!!");
        }
    }

    @Override
    public void fetchCompetitorAccounts(List<String> accountIdentifier, Long enterpriseId) {
        if(CollectionUtils.isNotEmpty(accountIdentifier)) {
            facebookCompetitorService.fetchCompetitorAccounts(accountIdentifier, enterpriseId);
        } else {
            log.info("Empty list received in input");
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, ErrorCodes.INVALID_REQUEST.name());
        }
    }

    @Override
    public void updateCompCache(SocialChannel channel, Long businessNumber) {
        facebookCompetitorService.updateCompCache(channel, businessNumber);
    }

    @Override
    public CompetitorListResponse getCompetitorList(Long businessNumber) {
        return facebookCompetitorService.getCompetitorList(businessNumber);
    }

    @Override
    public void deleteCompetitor(DeleteCompetitorDetailRequest deleteRequest, Long enterpriseId) throws SocialBirdeyeException {
        facebookCompetitorService.deleteCompetitor(deleteRequest, enterpriseId);
    }

    @Override
    public String getPageIdOnCompId(Integer rawCompId) {
        return facebookCompetitorService.getPageIdOnCompId(rawCompId);
    }

    @Override
    public void scanPages() {
        facebookCompetitorService.scanPages();
    }

    @Override
    public List<CompetitorBasicDetail> getCompetitorsBasicDetails(List<String> pageIds) {
        return facebookCompetitorService.getCompetitorsBasicDetails(pageIds);
    }

    @Override
    public void updateProfilePictureUrl(PicturesqueMediaCallback picturesqueMediaCallback, Integer rawCompId) {
        facebookCompetitorService.updateProfilePictureUrl(picturesqueMediaCallback, rawCompId);
    }

    @Override
    public void callPicturesQueForPage(PicturesqueCompRequest request) {
        facebookCompetitorService.callPicturesQueForPage(request);
    }

    @Override
    public CompetitorPageDetailResponse getPageSummary(String pageId, Long businessNumber) {
        return facebookCompetitorService.getPageSummary(pageId, businessNumber);
    }

    @Override
    public void proceedToUnmapPage(Long businessNumber) {
        facebookCompetitorService.proceedToUnmapPage(businessNumber);
    }

    @Override
    public Optional<Integer> fetchCompetitorProfileFollowerCount(CompetitorRequestDTO competitorRequestDTO) throws SocialBirdeyeException {
        return facebookCompetitorService.fetchCompetitorProfileFollowerCount(competitorRequestDTO);
    }

    @Override
    public CompetitorProfileReportESData getCompetitorProfileDataES(SearchRequest searchRequest, String channel) {
        return null;
    }

    @Override
    public Map<String, CompetitorPageDetails> getPageIdVsPageName(List<String> pageIds) throws SocialBirdeyeException {
        return facebookCompetitorService.getPageNameByPageId(pageIds);
    }

    @Override
    public Map<String, CompetitorPageDetails> getLocationIdVsPageId(List<String> pageIds) throws SocialBirdeyeException {
        // from raw table get pageId vs locationId map
        return fbSocialAccountService.findByFacebookPageIdsIn(pageIds);
    }

    @Override
    public  List<CompetitorPageDetailsDTO> getPageDetails(List<String> pageId) {
        return facebookCompetitorService.getPageDetails(pageId);
    }

    @Override
    public  List<CompetitorPageDetailsDTO> getSelfPageDetails(List<String> pageId) {
        return fbSocialAccountService.getPageDetails(pageId);
    }
    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) throws Exception {
        postOperationService.editPublishedPost(publishInfo);
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        postOperationService.deletePost(publishedPost);
    }

    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {
        if(Objects.isNull(pageInfoDto) || Objects.isNull(pageInfoDto.getAccessToken())) {
            return;
        }
        postOperationService.processPendingPosts(processingPost, pageInfoDto, publishInfo);
    }

    @Override
    public Map<String, PageInfoDto> getPageInfoDto(List<String> pageIds) {
        List<BusinessFBPage> fbPages = socialFBPageRepository.findByFacebookPageIdIn(pageIds);

        if (CollectionUtils.isEmpty(fbPages)) {
            return Collections.emptyMap();
        }

        return fbPages.stream()
                .collect(Collectors.toMap(
                        BusinessFBPage::getFacebookPageId,
                        page -> new PageInfoDto(page.getFacebookPageId(), page.getPageAccessToken()),
                        (existing, replacement) -> existing
                ));
    }


    @Override
    public Integer getCompetitorAccounts(Long enterpriseId) throws SocialBirdeyeException {
        return facebookCompetitorService.getCompetitorAccounts(enterpriseId);
    }

    @Override
    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return facebookInsights.createPostData(businessPosts,  new HashMap<>(), null);
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {
        facebookInsights.backfillEngagementBreakdown(backfillInsightReq);
    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {
        facebookInsights.backfillEngagementBreakdownPageWise(backfillRequest);
    }

    @Override
    public List<String> getPageIds(List<Integer> businessIds) {
        return socialFBPageRepository.findDistinctFacebookPageIdByBusinessIdInAndIsValid(businessIds);
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        return facebookInsights.getPostLeadershipReport(insightsRequest,postSortingCriteria,order,startIndex,pageSize);
    }


    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return socialFBPageRepository.findDistinctFacebookPageIdByBusinessIdInAndIsValid(businessIds);
    }

    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.isNull(searchTemplate)){
            return null;
        }
        return facebookInsights.getFacebookInsightsForTopPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds,List<LocationPagePair> locationPagePairList,
                                                      Map<String, Boolean> pagePermissionMap) {

        List<BusinessFBPage> facebookPages = socialFBPageRepository.findByBusinessIdIn(businessIds);
        if (CollectionUtils.isNotEmpty(facebookPages)) {
            facebookPages.forEach(page ->
                    updateLocationPagePairAndPagePermissionMap(locationPagePairList,
                            pagePermissionMap, page.getFacebookPageId(), page.getBusinessId(), page.getCanPost()));
        }
    }

    @Override
    public void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList,
                                                   Map<String, Boolean> pagePermissionMap) {
        List<BusinessFBPage> facebookPages = socialFBPageRepository.findByFacebookPageIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(facebookPages)) {
            facebookPages.forEach(page ->
                    updateLocationPagePairAndPagePermissionMap(locationPagePairList,
                            pagePermissionMap, page.getFacebookPageId(), page.getBusinessId(), page.getCanPost()));
        }
    }

    private void updateLocationPagePairAndPagePermissionMap(List<LocationPagePair> locations, Map<String, Boolean> pagePermissionMap,
                                                            String pageId, Integer businessId, Integer canPost) {
        locations.add(new LocationPagePair(pageId, businessId));
        if (pagePermissionMap != null) {
            pagePermissionMap.put(pageId, Objects.equals(canPost,1));
        }
    }

    @Override
    public List<String> getAllPageIds(int page, int size, Long enterpriseId) {
        return Objects.nonNull(enterpriseId)? socialFBPageRepository.findAllByEnterpriseId(enterpriseId):
        socialFBPageRepository.findPageIdsWithPagination(new PageRequest(page,size));
    }

}
