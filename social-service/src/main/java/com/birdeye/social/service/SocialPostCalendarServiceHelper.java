package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.*;
import com.birdeye.social.model.es.SocialPostCalendarMessage;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SocialPostCalendarServiceHelper {
    private static final Logger LOGGER = LoggerFactory.getLogger(SocialPostCalendarServiceHelper.class);

    @Autowired
    private IBusinessCoreService businessCoreService;


    @Autowired
    private SocialPostService socialPostService;


    @Autowired
    private PostAssetService postAssetService;


    @Autowired
    private PostActivityRepo postActivityRepo;


    @Autowired
    private SocialPostInfoRepository socialPostPublishInfoRepository;

    /*
     * Generate hasAccessMap
     * Step 1: For loop on the socialPostCalendarMessages posts
     * Step 2: For each post, create a list of locations associated with the social post
     * Step 3: Check if for a post, all its locations are present in the accessible locations set
     * Step 4: If not, add the channel to the hasIncompleteAccessMap against the postId
     */
    public Map<Integer, List<String>> getIncompleteAccessMap(
            GlobalFilterCriteriaSchedulePostMessage filter,
            List<SocialPostCalendarMessage> postMessages,
            Map<String, Integer> pageIdVsLocationIdMapComplete) throws InterruptedException, ExecutionException, TimeoutException {
        // If user has full access, then hasIncompleteAccessMap will be empty
        if(!Boolean.TRUE.equals(filter.getHasFullAccess())) {
            LOGGER.info("Generating hasIncompleteAccessMap");
            CompletableFuture<Map<Integer, List<String>>> future = CompletableFuture.supplyAsync(() ->
                    generateHasIncompleteAccessMap(filter.getAccessibleLocationIds(), postMessages, pageIdVsLocationIdMapComplete)
            );
            return future.get(10, TimeUnit.SECONDS);
        }else{
            return new HashMap<>();
        }
    }

    public Map<Integer, List<String>> generateHasIncompleteAccessMap(List<Integer> accessibleLocations,
                                                                   List<SocialPostCalendarMessage> socialPostCalendarMessages,
                                                                   Map<String, Integer> pageIdVsLocationIdMapComplete) {
        Map<Integer, List<String>> hasIncompleteAccessMap = new HashMap<>();
        if (CollectionUtils.isEmpty(accessibleLocations)) {
            return hasIncompleteAccessMap;
        }
        // Used for storing enterpriseLocations and preventing multiple calls to core
        Map<Integer, List<Integer>> enterpriseLocations = new HashMap<>();
        Set<Integer> accessibleLocationsSet = new HashSet<>(accessibleLocations);
        for (SocialPostCalendarMessage socialPostCalendarMessage : socialPostCalendarMessages) {
            List<Integer> accessibleLocationsForPost = getLocationsForPost(socialPostCalendarMessage, pageIdVsLocationIdMapComplete, enterpriseLocations);
            updateHasIncompleteAccessMap(hasIncompleteAccessMap, socialPostCalendarMessage, accessibleLocationsForPost);
        }
        return hasIncompleteAccessMap;
    }

    public List<Integer> getLocationsForPost(SocialPostCalendarMessage socialPostCalendarMessage, Map<String, Integer> pageIdVsLocationIdMapComplete,
                                              Map<Integer, List<Integer>> enterpriseLocations) {
        List<Integer> accessibleLocationsForPost = new ArrayList<>();
        if (CollectionUtils.isEmpty(socialPostCalendarMessage.getPageIds())) {
            Integer enterpriseId = socialPostCalendarMessage.getEnterpriseId();
            if(Objects.isNull(enterpriseId)) {
                accessibleLocationsForPost = socialPostService.fetchlocationsByScheduleCondition(socialPostCalendarMessage);
            }else {
                if (enterpriseLocations.containsKey(enterpriseId)) {
                    accessibleLocationsForPost = enterpriseLocations.get(enterpriseId);
                } else {
                    accessibleLocationsForPost = socialPostService.fetchlocationsByScheduleCondition(socialPostCalendarMessage);
                    enterpriseLocations.put(enterpriseId, accessibleLocationsForPost);
                }
            }
        } else {
            for (String pageId : socialPostCalendarMessage.getPageIds()) {
                if (pageIdVsLocationIdMapComplete.containsKey(pageId)) {
                    accessibleLocationsForPost.add(pageIdVsLocationIdMapComplete.get(pageId));
                }
            }
        }
        return accessibleLocationsForPost;
    }

    public void updateHasIncompleteAccessMap(Map<Integer, List<String>> hasIncompleteAccessMap,
                                    SocialPostCalendarMessage socialPostCalendarMessage,
                                    List<Integer> accessibleLocationsForPost) {
        // if accessibleLocationsForPost size is equal to the list of pages for which this post is scheduled, that means user has full access to the post
        if (socialPostCalendarMessage.getPageIds().size() != accessibleLocationsForPost.size()) {
            String channel = SocialChannel.getSocialChannelNameById(socialPostCalendarMessage.getSourceId());
            List<String> channelList = hasIncompleteAccessMap.getOrDefault(socialPostCalendarMessage.getId(), new ArrayList<>());
            if (!channelList.contains(channel)) {
                channelList.add(channel);
            }
            hasIncompleteAccessMap.put(socialPostCalendarMessage.getId(), channelList);
        }
    }

    public Future<Map<Integer, BusinessCoreUser>> fetchUserDetailsAsync(Set<Integer> userIds, ThreadPoolTaskExecutor executor) {
        return executor.submit(() -> {
            if (CollectionUtils.isNotEmpty(userIds)) {
                return businessCoreService.getBusinessUserForUserId(new ArrayList<>(userIds));
            }
            return new HashMap<>();
        });
    }

    public Map<Integer, SocialPostsAssets> fetchPostAssetsAsync(Set<Integer> assetIds) throws Exception {
        CompletableFuture<Map<Integer, SocialPostsAssets>> assetsFuture =
                CompletableFuture.supplyAsync(() -> postAssetService.getPostAssetsMapFromIds(assetIds));
        CompletableFuture.allOf(assetsFuture).get(100, TimeUnit.SECONDS);
        return assetsFuture.get();
    }

    public Map<Integer, Integer> fetchDuplicatePostCounts(Set<Integer> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return new HashMap<>();
        }
        List<PostActivityRepo.ActivityCount> postVsDuplicateCount =
                postActivityRepo.getPostWiseActivityCount(postIds, PostActivityType.DUPLICATE.getName());

        return postVsDuplicateCount.stream()
                .collect(Collectors.toMap(
                        PostActivityRepo.ActivityCount::getPostId,
                        PostActivityRepo.ActivityCount::getActivityCount
                ));
    }


    public Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> fetchPostPageStatuses(Set<Integer> postIds,
                                                                                             List<Integer> sourceIds) {
        List<SocialPostInfoRepository.PostPageStatus> statusList =
                socialPostPublishInfoRepository.findBySocialPostIdInAndSourceIdIn(postIds, sourceIds);
        LOGGER.info("postPageStatusList : {}", statusList);
        if (CollectionUtils.isEmpty(statusList)) return new HashMap<>();
        return statusList.stream()
                .collect(Collectors.groupingBy(SocialPostInfoRepository.PostPageStatus::getSocialPostId));
    }

    /*
        * This method is used to find the applicable business ids from two lists.
        * We get businessIds from UI (if specific locations are selected).
        * In that case we need to return posts related to those businessIds only.
        * businessIds is a subset of accessibleLocationIds.
     */
    public List<Integer> findApplicableBusinessIds(List<Integer> accessibleLocationIds, List<Integer> businessIds) {
        // No location is accessible to user
        if (CollectionUtils.isEmpty(accessibleLocationIds)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(businessIds)) {
            return accessibleLocationIds;
        } else {
            // Intersecting businessIds with accessibleLocationIds
            Set<Integer> set1 = new HashSet<>(accessibleLocationIds);
            return businessIds.stream()
                    .filter(set1::contains)
                    .collect(Collectors.toList());
        }
    }

    public void fillValuesFromCalendarEsList(List<SocialPostCalendarMessage> socialSchedulePostMessageESList,
                                             Set<Integer> userIds, Set<Integer> socialPostAssetIds, Set<Integer> distinctPostIds,
                                             List<SocialPostCalendarMessage> applePosts){
        for (SocialPostCalendarMessage message : socialSchedulePostMessageESList) {
            if (message.getCreatedBy() != null) {
                userIds.add(message.getCreatedBy());
            }
            if (CollectionUtils.isNotEmpty(message.getVideoIds())) {
                socialPostAssetIds.addAll(message.getVideoIds());
            }
            if (CollectionUtils.isNotEmpty(message.getImageIds())) {
                socialPostAssetIds.addAll(message.getImageIds());
            }
            if (CollectionUtils.isNotEmpty(message.getCompressedImages())) {
                socialPostAssetIds.addAll(message.getCompressedImages());
            }
            if (message.getId() != null) {
                distinctPostIds.add(message.getId());
            }
            if (applePosts!= null && message.getSourceId() != null
                    && message.getSourceId().equals(SocialChannel.APPLE_CONNECT.getId())) {
                applePosts.add(message);
            }
        }
    }

    public Map<String, List<SocialPostCalendarMessage>>  createPaginationForEsListView(List<SocialPostCalendarMessage> socialPosts, Integer pageNo,
                                                                                        Integer pageSize, PageSortDirection direction,
                                                                                       SocialSchedulePostResponse socialSchedulePostResponse) {
        socialPosts.sort(Comparator
                .comparing(SocialPostCalendarMessage::getDatePublish,
                        direction.equals(PageSortDirection.ASC) ? Comparator.naturalOrder() : Comparator.reverseOrder())
                .thenComparing(SocialPostCalendarMessage::getId));

        int startIndex = pageNo * pageSize;
        int endIndex = Math.min(startIndex + pageSize, socialPosts.size());
        if(endIndex < socialPosts.size()) socialSchedulePostResponse.setHasMorePosts(true);
        if (startIndex < endIndex) {
            socialPosts = socialPosts.subList(startIndex, endIndex);
        } else {
            socialPosts = new ArrayList<>();
        }
        return groupByFormattedPublishDate(socialPosts);
    }

    public Map<String, List<SocialPostCalendarMessage>> groupByFormattedPublishDate(List<SocialPostCalendarMessage> socialPosts) {
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM/dd/yyyy");
        return socialPosts.stream()
                .filter(post -> post.getPublishDate() != null)
                .collect(Collectors.groupingBy(
                        post -> outputFormat.format(post.getPublishDate()),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
    }


}
