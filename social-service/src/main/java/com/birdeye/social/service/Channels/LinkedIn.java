package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dao.SocialStreamsRepository;
import com.birdeye.social.dto.PostInsightDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.EngageFeedDetails;
import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadRequest;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.service.SocialRawPageDetail;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.PageInsightV2EsData;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.linkedin.LinkedinService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.FbNotification.FreshPostNotificationRequest;
import com.birdeye.social.model.engageV2.EngageBusinessDetails;
import com.birdeye.social.model.engageV2.EngageCommentRequest;
import com.birdeye.social.model.engageV2.EngageWebhookSubscriptionRequest;
import com.birdeye.social.model.engageV2.PageDetailsData;
import com.birdeye.social.model.engageV2.message.ExternalServiceEvent;
import com.birdeye.social.model.engageV2.message.InboxMessageRequest;
import com.birdeye.social.service.SocialEngageService.SocialEngageV2;
import com.birdeye.social.service.SocialPostOperationService.LinkedIn.LinkedInPostOperationService;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialPostLinkedinService;
import com.birdeye.social.service.SocialMediaUploadService;
import com.birdeye.social.service.SocialReportService.LinkedIn.LinkedInInsights;
import com.birdeye.social.service.SocialReportService.LinkedIn.LinkedInPageService;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class LinkedIn implements SocialInsights, SocialEngageV2 , SocialMediaUploadService, PostOperation {

    public final String LINKEDIN_SHARE_URL = "https://www.linkedin.com/feed/update/";
    @Autowired
    private LinkedInInsights linkedInInsights;

    @Autowired
    private LinkedInPageService linkedInPageService;

    @Autowired
    private BusinessLinkedinPageRepository businessLinkedinPageRepo;

    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private LinkedinService linkedinService;
    @Autowired
    private SocialStreamsRepository socialStreamsRepository;
    @Autowired
    private LinkedInPostOperationService postOperationService;

    @Autowired
    private BusinessLinkedinPageRepository linkedinPageRepository;
    @Autowired
    private SocialPostLinkedinService socialPostLinkedinService;

    private static final Logger LOG = LoggerFactory.getLogger(LinkedIn.class);

    @Override
    public String channelName() {
        return SocialChannel.LINKEDIN.getName();
    }

    @Override
    @Cacheable(value = "linkedinPage", key = "#pageId+'_'+'linkedin'", unless = "#result == null")
    public SocialRawPageDetail getPageDetails(String pageId) {
        return linkedInPageService.getPageDetails(pageId);
    }

    @Override
    public void registerMedia(Integer socialMediaUploadRequestId, SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaInitiateRequest) {
        socialPostLinkedinService.registerMedia(socialMediaUploadRequestId, socialRawPageDetail, mediaInitiateRequest);
    }

    @Override
    public void uploadChunk(SocialAssetChunkInfo socialAssetChunkInfo, SocialMediaUploadInfo socialMediaUploadInfo,
                            SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest, boolean isV2request) {
        socialPostLinkedinService.uploadChunk(socialAssetChunkInfo,socialMediaUploadInfo,socialRawPageDetail, socialMediaUploadRequest, isV2request);
    }

    @Override
    public void uploadCaption(SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest) {
        socialPostLinkedinService.uploadCaption(socialMediaUploadRequest,socialRawPageDetail);
    }

    @Override
    public void finalizeVideoUpload(SocialRawPageDetail socialRawPageDetail,
                                    SocialMediaUploadRequest socialMediaUploadRequest,
                                    List<String> eTags) {
        socialPostLinkedinService.finalizeVideoUpload(socialMediaUploadRequest,socialRawPageDetail, eTags);
    }

    @Override
    public void postCaption(SocialMediaUploadRequest socialMediaUploadRequest, SocialRawPageDetail socialRawPageDetail) {
//        socialPostLinkedinService.
    }

    @Override
    public void checkStatus(SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaUploadRequest) {
        socialPostLinkedinService.checkStatus(socialRawPageDetail, mediaUploadRequest);
    }

    @Override
    public void birdeyeExceptionHandler(BirdeyeSocialException bse,Integer publishInfoId,String pageId) {
        socialPostLinkedinService.birdeyeExceptionHandler(bse,publishInfoId,pageId);
    }

    @Override
    public void generalExceptionHandler(String message, Integer publishInfoId) {
        socialPostLinkedinService.generalExceptionHandler(message,publishInfoId);
    }

    @Override
    public void postContentWithMedia(MediaUploadRequest request, String pageId,SocialPostPublishInfo publishInfoId) throws Exception {
        socialPostLinkedinService.postContentWithMedia(request,pageId,publishInfoId);
    }

    @Override
    @CacheEvict(value = "linkedinPage", key = "#externalPageId+'_'+'linkedin'")
    public void evictPageCache(String externalPageId) {
        LOG.info("Evict cache for page Id:{}",externalPageId);
    }

    @Override
    public SocialTimeline getFeedData(String pageId, EngageFeedDetails feedDetails, String type) {
        return linkedInPageService.getFeedData(pageId,feedDetails,type);
    }

    @Override
    public List<EngageNotificationDetails> getFeedEngagement(String pageId) {
        return null;
    }

    @Override
    public List<EngageNotificationDetails> getCommentData(EngageCommentRequest request) {
        return linkedInPageService.getCommentData(request);
    }

    @Override
    public EngageNotificationDetails prepareAdditionalParamentToEs(EngageNotificationDetails request) {
        return request;
    }

    @Override
    public Feed getPostDetails(FreshPostNotificationRequest request) {
        return linkedInPageService.getPostDetails(request);
    }

    @Override
    public void startBackFill(GenericScriptRequest scriptRequest) {
        EngageWebhookSubscriptionRequest engageWebhookSubscriptionRequest = new EngageWebhookSubscriptionRequest();
        engageWebhookSubscriptionRequest.setChannel(Constants.LINKEDIN);
        engageWebhookSubscriptionRequest.setSubscription(true);

        List<Integer> socialStreamsList = socialStreamsRepository.findSocialIdByChannelId(SocialChannel.LINKEDIN.getId());
        Set<String> streamPageIds = new HashSet<>();
        if(CollectionUtils.isNotEmpty(socialStreamsList)) {
            streamPageIds = businessLinkedinPageRepo.findProfileIdById(socialStreamsList);
        }
        Set<String> finalStreamPageIds = streamPageIds;
        if(CollectionUtils.isNotEmpty(scriptRequest.getBusinessIds())){
            List<String> businessLinkedinProfileIds = businessLinkedinPageRepo.findDistinctPageIdByBusinessIdIn(scriptRequest.getBusinessIds());
            if(CollectionUtils.isEmpty(businessLinkedinProfileIds)){
                LOG.info("No data left for back filling engage");
                return;
            }
            businessLinkedinProfileIds.forEach(pageId -> {
                if(finalStreamPageIds.contains(pageId) && scriptRequest.getBackFill()) {
                    engageWebhookSubscriptionRequest.setBackFill(true);
                }
                engageWebhookSubscriptionRequest.setPageId(pageId);
                kafkaProducerService.sendObjectV1(Constants.SOCIAL_LINKEDIN_SUBSCRIBE, engageWebhookSubscriptionRequest);
            });
            return;
        }
        int page = scriptRequest.getPage();
        int size = scriptRequest.getSize();
        while (true) {
            List<String> businessLinkedinProfileIds = businessLinkedinPageRepo.findValidBusinessPageIds(new PageRequest(page, size));
            if(CollectionUtils.isEmpty(businessLinkedinProfileIds)){
                LOG.info("No data left for back filling engage");
                break;
            }
            businessLinkedinProfileIds.forEach(pageId -> {
                if(finalStreamPageIds.contains(pageId) && scriptRequest.getBackFill()) {
                    engageWebhookSubscriptionRequest.setBackFill(true);
                }
                engageWebhookSubscriptionRequest.setPageId(pageId);
                kafkaProducerService.sendObjectV1(Constants.SOCIAL_LINKEDIN_SUBSCRIBE, engageWebhookSubscriptionRequest);
            });
            page++;
        }

    }

    @Override
    public void saveMessages(InboxMessageRequest inboxMessageRequest) {

    }

    @Override
    public EngageBusinessDetails getChannelEnterpriseIdByPageId(String pageId) {
        EngageBusinessDetails engageBusinessDetails = new EngageBusinessDetails();
        BusinessLinkedinPage businessLinkedinPage = businessLinkedinPageRepo.findByProfileId(pageId);
        if(businessLinkedinPage != null){
            engageBusinessDetails.setEnterpriseId(businessLinkedinPage.getEnterpriseId());
            engageBusinessDetails.setBusinessIds(Arrays.asList(businessLinkedinPage.getBusinessId()));
            return engageBusinessDetails;
        }
        return null;
    }

    @Override
    public Feed getFeedDetails(String pageId, Feed feed) {
        return null;
    }

    @Override
    public void hideWallPost(SocialEngageObjectRequest request) {

    }

    @Override
    public void hidePostComment(SocialEngageObjectRequest request) {

    }

    @Override
    public void blockUserFromPage(SocialEngageObjectRequest request, ExternalServiceEvent blockUserEvent) {

    }

    @Override
    public void unBlockUserFromPage(SocialEngageObjectRequest request, ExternalServiceEvent blockUserEvent) {

    }

    @Override
    public void likePageContent(SocialEngageObjectRequest request,ExternalServiceEvent externalServiceEvent){
        linkedInPageService.likePostFromLinkedIn(request);
    }

    @Override
    public void unLikePageContent(SocialEngageObjectRequest request,ExternalServiceEvent externalServiceEvent) {
        linkedInPageService.unLikePostFromLinkedIn(request);
    }

    @Override
    public void commentPageContent(SocialEngageObjectRequest request) {
        linkedInPageService.commentOnPost(request);
    }

    @Override
    public void deletePageContent(SocialEngageObjectRequest request) {
        linkedInPageService.deletePostContent(request);
    }

    @Override
    public Boolean subscribeNotificationWebhook(String pageId) {
        return linkedInPageService.subscribeToWebhook(pageId);
    }

    @Override
    public void unSubscribeNotificationWebhook(String pageId) {
        linkedInPageService.unSubscribeToWebhook(pageId);
    }

    @Override
    public void followUser(SocialEngageObjectRequest request) {

    }

    @Override
    public void unfollowUser(SocialEngageObjectRequest request) {

    }

    @Override
    public void shareComment(SocialEngageObjectRequest request) {

    }

    @Override
    public void unShareComment(SocialEngageObjectRequest request) {

    }

    @Override
    public void saveCommentInDbAndInES(SocialEngageObjectRequest request, EngageNotificationDetails documentFromEs) {
        linkedInPageService.postDataInEs(request,documentFromEs);
    }

    @Override
    public void likeMessage(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent) {

    }

    @Override
    public void unLikeMessage(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent) {

    }

    @Override
    public List<String> getChannelPageIdsByEnterpriseId(Long enterpriseId) {
        return businessLinkedinPageRepo.findByEnterpriseIdAndIsValidAndMapped(enterpriseId);
    }

    @Override
    public  PageDetailsData getChannelPageIdsByEnterpriseIds(List<Long> enterpriseId) {
        List<BusinessLinkedinPage> pageDetails= businessLinkedinPageRepo.findDataByEnterpriseIdAndIsValidAndMapped(enterpriseId);
        List<PageDetailsData.PageDetails> pageDetailsList = pageDetails.stream()
                .map(page -> {
                    PageDetailsData.PageDetails pageDetailsObj = new PageDetailsData.PageDetails();
                    pageDetailsObj.setPageId(page.getProfileId()); // Assuming getPageId() exists
                    pageDetailsObj.setBusinessId(page.getBusinessId()); // Assuming getBusinessId() exists
                    pageDetailsObj.setAccountId(page.getAccountId()); // Assuming getAccountId() exists
                    return pageDetailsObj;
                })
                .collect(Collectors.toList());
        PageDetailsData data = new PageDetailsData();
        data.setPageDetailsList(pageDetailsList);
        return data;
    }

    @Override
    public void subscribeEngageNotificationWebhook(String pageId) {
        linkedInPageService.subscribeToWebhook(pageId);
    }

    @Override
    public void unSubscribeEngageNotificationWebhook(String pageId) {
        linkedInPageService.unSubscribeToWebhook(pageId);
    }

    @Override
    public void removePageNotificationWebhook(EngageWebhookSubscriptionRequest request) {
        linkedInPageService.removePageSubscriptionToWebhook(request);
    }

    @Override
    public String getReviewerUrlByPageId(String pageId) {
         BusinessLinkedinPage page=businessLinkedinPageRepo.findByProfileId(pageId);
         if(Objects.nonNull(page))
         {
             return page.getPageUrl();
         }
         return "";
    }

    @Override
    public EngagePageDetails getRawPageDetails(String pageId) {
        BusinessLinkedinPage page=businessLinkedinPageRepo.findByProfileId(pageId);
        if(Objects.nonNull(page))
        {
            return EngagePageDetails.builder()
                    .pageName(StringUtils.isEmpty(page.getCompanyName()) ? page.getFirstName() : page.getCompanyName())
                    .username(StringUtils.isEmpty(page.getCompanyName()) ? page.getFirstName() : page.getVanityName())
                    .pageId(page.getProfileId())
                    .profilePicUrl(page.getLogoUrl())
                    .build();
        }
        return EngagePageDetails.builder().build();
    }

    @Override
    public Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception {
        LOG.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        LOG.info("Template is {}",searchTemplate);
        if(Objects.nonNull(searchTemplate)) {
            return linkedInInsights.getLinkedInInsightsForPage(insightsRequest);
        }
        return null;
    }

    @Override
    public Object getPageInsightsESData(InsightsRequest insightsRequest) throws Exception {
        LOG.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        LOG.info("Template is {}",searchTemplate);

        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.equals(searchTemplate)){
            PageInsightV2EsData engageData = linkedInInsights.getLinkedInInsightsForMessageSent(insightsRequest);

            PageInsightV2EsData postData = linkedInInsights.getLinkedInInsightsForPublishPost(insightsRequest);
            if(Objects.nonNull(postData)) {
                engageData.setBuckets(postData.getBuckets());
                engageData.setCurrentData(postData.getCurrentData());
            }
            insightsRequest.setReportType(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.getName());
            return engageData;
        } else if (SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC.equals(searchTemplate)) {
            return linkedInInsights.getLinkedInInsightsForPublishPost(insightsRequest);
        } else {
            return linkedInInsights.getPageInsightsESData(insightsRequest);
        }
    }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {
        if(Objects.isNull(pageInsights)){
            return;
        }
        linkedInInsights.postLinkedInPageInsightToES(pageInsights);
    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {
        if(CollectionUtils.isEmpty(businessPosts)){
            return;
        }
        linkedInInsights.getPostInsights(businessPosts.get(0),isFreshRequest);
    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO) {
        linkedInInsights.getPageInsightsFromLinkedin(socialScanEventDTO);
    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {
        linkedInInsights.postLinkedinPostInsightsToEs(postData);
    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return linkedInInsights.getLinkedInInsightsForPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) {
        // @ashutosh
        // update posts from birdeye
        // fetch insights instant
        // not wait for cron to fetch insights next day
    }

    @Override
    public void updatePageInsightsDb( String pageId, Integer businessId, Long enterpriseId) {

    }

    public void updatePageInsightsPostCount(PageInsights pageInsights) {

    }

    @Override
    public String getPageInsightIndexName() {
        return ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName();
    }

    @Override
    public void startScanForPosts(String pageId) {

    }

	@Override
	public void getGMBPageAnalytics(String pageId, Integer businessId) {
		// TODO Auto-generated method stub

	}

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {
        if(Objects.isNull(businessPosts)){
            return;
        }
        linkedInInsights.saveCDNPostToES(businessPosts);
    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {
        // TODO Auto-generated method stub

    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest) {
        return linkedInInsights.getLinkedInPerformanceData(insightsRequest);
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return linkedInInsights.backfillProfilePagesToEs(pageInsights);
    }
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        LOG.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        LOG.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.REPORT_MESSAGE_VOLUME.equals(searchTemplate)){
            return linkedInInsights.getMessageVolumeInsightsReportData(insightsRequest);
        } else {
            return linkedInInsights.getLinkedInInsightsReportData(insightsRequest);
        }

    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        List<BusinessLinkedinPage> linkedinPages = businessLinkedinPageRepo.findAllByBusinessIdIn(businessIds);
        if(CollectionUtils.isNotEmpty(linkedinPages)){
            Map<String, Integer> businessIdPageIdMap = new HashMap<>();
            linkedinPages.forEach(account -> {
                businessIdPageIdMap.put(account.getProfileId(), account.getBusinessId());
            });
            return businessIdPageIdMap;
        }
        return null;
    }

    @Override
    public boolean isPageValid(String pageId)
    {
        BusinessLinkedinPage businessLinkedinPage=businessLinkedinPageRepo.findByProfileId(pageId);
        if(Objects.nonNull(businessLinkedinPage) && Objects.nonNull(businessLinkedinPage.getIsValid())
                && businessLinkedinPage.getIsValid()==1)
        {
            return true;
        }
        return false;
    }

    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return linkedInInsights.createPostData(businessPosts, null, new PostInsightDTO(), 0);
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {
        linkedInInsights.backfillEngagementBreakdown(backfillInsightReq);
    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {
        linkedInInsights.backfillEngagementBreakdownPageWise(backfillRequest);
    }

    @Override
    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return businessLinkedinPageRepo.findDistinctPageIdByBusinessIdIn(businessIds);
    }

    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) {
        postOperationService.editPost(publishInfo);
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        postOperationService.deletePost(publishedPost);
    }

    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {

    }

    @Override
    public Map<String, PageInfoDto> getPageInfoDto(List<String> pageIds) {
        return Collections.emptyMap();
    }


    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return linkedInInsights.getLinkedInInsightsForTopPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds,List<LocationPagePair> locationPagePairList,
                                                      Map<String, Boolean> pagePermissionMap) {
        List<BusinessLinkedinPage> linkedinPages = businessLinkedinPageRepo.findByBusinessIdIn(businessIds);
        if (CollectionUtils.isNotEmpty(linkedinPages)) {
            linkedinPages.forEach(page ->
                    updateLocationPagePairAndPagePermissionMap(
                            locationPagePairList, pagePermissionMap, page.getProfileId(), page.getBusinessId(), page.getCanPost()));
        }
    }

    @Override
    public void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList,
                                                   Map<String, Boolean> pagePermissionMap) {
        List<BusinessLinkedinPage> linkedinPages = businessLinkedinPageRepo.findByProfileIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(linkedinPages)) {
            linkedinPages.forEach(page ->
                    updateLocationPagePairAndPagePermissionMap(
                    locationPagePairList, pagePermissionMap, page.getProfileId(), page.getBusinessId(), page.getCanPost()));
        }
    }

    private void updateLocationPagePairAndPagePermissionMap(List<LocationPagePair> locations, Map<String, Boolean> pagePermissionMap,
                                                            String pageId, Integer businessId, Integer canPost) {
        locations.add(new LocationPagePair(pageId, businessId));
        if (pagePermissionMap != null) {
            pagePermissionMap.put(pageId, Objects.equals(canPost,1));
        }
    }

    public List<String> getPageIds(List<Integer> businessIds) {
        return businessLinkedinPageRepo.findDistinctPageIdByBusinessIdInAndIsValid(businessIds);
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        return linkedInInsights.getPostLeadershipReport(insightsRequest,postSortingCriteria,order,startIndex,pageSize);
    }

    @Override
    public boolean validateImageUrls(List<String> imageUrls) {
        return true;
    }

    @Override
    public boolean validateCommentDetails(List<EngageNotificationDetails> comments, String commentId) {
        return true;
    }

    @Override
    public List<String> getAllPageIds(int page, int size, Long enterpriseId) {
        return Objects.nonNull(enterpriseId)? linkedinPageRepository.findProfileIdByEnterpriseId(enterpriseId):
                linkedinPageRepository.findProfileIdsWithPagination(new PageRequest(page,size));
    }
}
