package com.birdeye.social.service.SocialReportService;

import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.LocationPagePair;
import org.elasticsearch.search.sort.SortOrder;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface SocialInsights {

    String channelName();

    Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception;

    Object getPageInsightsESData(InsightsRequest insightsRequest) throws Exception;

    Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception;

    void postPageInsightsToEs(PageInsights pageInsights);

    void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest);

    void getPageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO);

    void postPostDataAndInsightsToEs(PostData postData);

    Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload);

    void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) throws IOException;

    void updatePageInsightsDb(String pageId,Integer businessId, Long enterpriseId);

    void updatePageInsightsPostCount(PageInsights pageInsights);

    String getPageInsightIndexName();

    void startScanForPosts(String pageId);

    void getGMBPageAnalytics(String pageId,Integer businessId) throws Exception;

    void saveCDNPostToES(BusinessPosts businessPosts);

    void getGMBKeywordAnalytics(Integer businessId) throws Exception;

    PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest);

    boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights);
    Object getPageReportESData(InsightsRequest insightsRequest) throws Exception;

    Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds);

    PostData createPostData(BusinessPosts businessPosts) throws Exception;

    void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq);

    void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest);

    List<String> getPageIds(List<Integer> businessIds);

    LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                        ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                        Integer startIndex, Integer pageSize);

    List<String> getPageIdsFromBusinessIds(List<Integer> businessIds);

    Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload);

    void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds, List<LocationPagePair> locationPagePairList,
                                                                 Map<String, Boolean> pagePermissionMap);

    void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList,
                                                              Map<String, Boolean> pagePermissionMap);


}
