package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessYoutubeChannelRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.LocationPagePair;
import com.birdeye.social.model.PageInfoDto;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialPostOperationService.Youtube.YoutubePostOperationService;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.service.Youtube.YoutubeInsights;
import com.birdeye.social.service.Youtube.YoutubeReportServiceImpl;
import com.birdeye.social.service.notification.NotificationAuditService;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
public class Youtube implements SocialInsights, NotificationAuditService, PostOperation {

    @Autowired
    private YoutubeReportServiceImpl youtubeReportService;

    @Autowired
    private YoutubeInsights youtubeInsights;

    @Autowired
    private BusinessYoutubeChannelRepository youtubeChannelRepository;

    @Autowired
    private YoutubePostOperationService postOperationService;

    private static final Logger log = LoggerFactory.getLogger(Youtube.class);

    @Override
    public String channelName() {  return SocialChannel.YOUTUBE.getName();}

    @Override
    public List<SocialNotificationAudit> putNotificationForChannel(Object notificationObject) {
        return null;
    }

    @Override
    public Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {
        if(Objects.isNull(pageInsights)) return;

        youtubeReportService.postPageInsightsToEs(pageInsights);
    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {

    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO) {
        youtubeReportService.generatePageInsightsFromSocialChannel(socialScanEventDTO);
    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {

    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return null;
    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) throws IOException {

    }

    @Override
    public void updatePageInsightsDb(String pageId, Integer businessId, Long enterpriseId) {

    }

    @Override
    public void updatePageInsightsPostCount(PageInsights pageInsights) {

    }

    @Override
    public String getPageInsightIndexName() {
        return null;
    }

    @Override
    public void startScanForPosts(String pageId) {

    }

    @Override
    public void getGMBPageAnalytics(String pageId, Integer businessId) throws Exception {

    }

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {

    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {

    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest) {
        return youtubeInsights.getYoutubePerformanceData(insightsRequest);
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return youtubeInsights.backfillProfilePagesToEs(pageInsights);
    }
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.REPORT_MESSAGE_VOLUME.equals(searchTemplate)){
            return null;
        } else {
            return youtubeInsights.getYoutubeInsightsReportData(insightsRequest);
        }
    }

    @Override
    public Object getPageInsightsESData(InsightsRequest insightsRequest) throws Exception {

        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);

        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.equals(searchTemplate)){
            return youtubeInsights.getYoutubeInsightsForMessageVolume(insightsRequest);
        } else if (SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC.equals(searchTemplate)) {
            return youtubeInsights.getYoutubeInsightsForPublishPost(insightsRequest);
        }  else {
            return youtubeInsights.getYoutubeInsightsESData(insightsRequest);
        }
    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findAllByBusinessIdIn(businessIds);
        if(CollectionUtils.isNotEmpty(youtubeChannels)){
            Map<String, Integer> businessIdPageIdMap = new HashMap<>();
            youtubeChannels.forEach(youtubeChannel -> businessIdPageIdMap.put(youtubeChannel.getChannelId(), youtubeChannel.getBusinessId()));
            return businessIdPageIdMap;
        }
        return Collections.emptyMap();
    }

    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return null;
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {

    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {

    }

    @Override
    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return youtubeChannelRepository.findDistinctChannelIdByBusinessIdIn(businessIds);
    }


    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) {
        postOperationService.editPost(publishInfo);
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        postOperationService.deletePost(publishedPost);
    }

    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {

    }

    @Override
    public Map<String, PageInfoDto> getPageInfoDto(List<String> pageIds) {
        return Collections.emptyMap();
    }


    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return null;
    }

    @Override
    public void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds,List<LocationPagePair> locationPagePairList,
                                                      Map<String, Boolean> pagePermissionMap) {
        List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByBusinessIdIn(businessIds);
        if (CollectionUtils.isNotEmpty(youtubeChannels)) {
            youtubeChannels.forEach(channel ->
                    updateLocationPagePairAndPagePermissionMap(locationPagePairList,
                            pagePermissionMap, channel.getChannelId(), channel.getBusinessId(), channel.getCanPost()));
        }
    }

    @Override
    public void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList,
                                                   Map<String, Boolean> pagePermissionMap) {
        List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByChannelIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(youtubeChannels)) {
            youtubeChannels.forEach(channel ->
                    updateLocationPagePairAndPagePermissionMap(locationPagePairList,
                            pagePermissionMap, channel.getChannelId(), channel.getBusinessId(), channel.getCanPost()));
        }
    }

    private void updateLocationPagePairAndPagePermissionMap(List<LocationPagePair> locations, Map<String, Boolean> pagePermissionMap,
                                                           String pageId, Integer businessId, Integer canPost) {
        locations.add(new LocationPagePair(pageId, businessId));
        if (pagePermissionMap != null) {
            pagePermissionMap.put(pageId, Objects.equals(canPost,1));
        }
    }

    public List<String> getPageIds(List<Integer> businessIds) {
        return youtubeChannelRepository.findDistinctChannelIdByBusinessIdInAndIsValid(businessIds);
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        return youtubeInsights.getPostLeadershipReport(insightsRequest,postSortingCriteria,order,startIndex,pageSize);
    }

    @Override
    public List<String> getAllPageIds(int page, int size, Long enterpriseId) {
        return Objects.nonNull(enterpriseId)? youtubeChannelRepository.findDistinctYoutubeChannelsIdByBusinessIdIn(enterpriseId):
                youtubeChannelRepository.findChannelIdsWithPagination(new PageRequest(page,size));
    }
}
