package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessTiktokAccountsRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessTiktokAccounts;
import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.*;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.LocationPagePair;
import com.birdeye.social.model.PageInfoDto;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.service.SocialReportService.Tiktok.TiktokInsights;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.birdeye.social.tiktok.TikTokFeedData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
public class Tiktok implements SocialInsights, PostOperation {

    @Autowired
    private TiktokInsights tiktokInsights;

    @Autowired
    private BusinessTiktokAccountsRepository businessTiktokAccountsRepository;
    private static final Logger log = LoggerFactory.getLogger(Tiktok.class);
    @Autowired
    private BusinessTiktokAccountsRepository tiktokAccountsRepository;

    @Override
    public String channelName() {
        return SocialChannel.TIKTOK.getName();
    }

    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) throws Exception {
        //not supported for tiktok
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        //not supported for tiktok
    }

    @Override
    public Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        if(Objects.nonNull(insightsRequest.getReportType())
                && ( Objects.equals(insightsRequest.getReportType(),"gender_based_insights")
                        || Objects.equals(insightsRequest.getReportType(),"country_based_insights")
                        || Objects.equals(insightsRequest.getReportType(),"city_based_insights") )) {
            return tiktokInsights.getDemographicsInsights(insightsRequest);
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.nonNull(searchTemplate)) {
            return tiktokInsights.getTiktokInsightsForPage(insightsRequest);
        }
        return null;
    }

    @Override
    public PageInsightV2EsData getPageInsightsESData(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }
        if(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.equals(searchTemplate)){
            PageInsightV2EsData engageData = tiktokInsights.getTiktokInsightsForMessageSent(insightsRequest);
            PageInsightV2EsData postData = tiktokInsights.getTiktokInsightsForPublishPost(insightsRequest);
            if(Objects.nonNull(postData)) {
                engageData.setBuckets(postData.getBuckets());
                engageData.setCurrentData(postData.getCurrentData());
            }
            insightsRequest.setReportType(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.getName());
            return engageData;
        } else if(SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC.equals(searchTemplate)) {
            return tiktokInsights.getTiktokInsightsForPublishPost(insightsRequest);
        } else
            return tiktokInsights.getTiktokInsightsESData(insightsRequest);

    }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {
        if(Objects.isNull(pageInsights)){
            return;
        }
        tiktokInsights.postTiktokPageInsightToES(pageInsights);
    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {
        tiktokInsights.getPostInsights(businessPosts.get(0), isFreshRequest);
    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO) {
        tiktokInsights.getPageInsightsFromTiktok(socialScanEventDTO);
    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {
        tiktokInsights.tiktokPostDataAndInsightsToEs(postData);
    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return tiktokInsights.getTiktokInsightsForPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) throws IOException {

    }

    @Override
    public void updatePageInsightsDb(String pageId, Integer businessId, Long enterpriseId) {

    }

    @Override
    public void updatePageInsightsPostCount(PageInsights pageInsights) {

    }

    @Override
    public String getPageInsightIndexName() {
        return "";
    }

    @Override
    public void startScanForPosts(String pageId) {

    }

    @Override
    public void getGMBPageAnalytics(String pageId, Integer businessId) throws Exception {

    }

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {

    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {

    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest) {
        return tiktokInsights.getTiktokPerformanceData(insightsRequest);
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return false;
    }

    @Override
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        log.info("Tiktok downlaod report request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }
        if(SearchTemplate.REPORT_MESSAGE_VOLUME.equals(searchTemplate)){
            return new PageReportEsData();
        }
        else {
            return tiktokInsights.getTiktokInsightsReportData(insightsRequest);
        }
    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        List<BusinessTiktokAccounts> businessTiktokAccounts = businessTiktokAccountsRepository.findAllByBusinessIdIn(businessIds);
        if(CollectionUtils.isNotEmpty(businessTiktokAccounts)){
            Map<String, Integer> businessIdPageIdMap = new HashMap<>();
            businessTiktokAccounts.forEach(page -> {
                businessIdPageIdMap.put(page.getProfileId(), page.getBusinessId());
            });
            return businessIdPageIdMap;
        }
        return null;
    }

    @Override
    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return tiktokInsights.createPostData(businessPosts, new TikTokFeedData());
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {

    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {

    }

    @Override
    public List<String> getPageIds(List<Integer> businessIds) {
//        return Collections.emptyList();
        return businessTiktokAccountsRepository.findDistinctProfileIdByBusinessIdInAndIsValid(businessIds);
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest, ReportSortingCriteria postSortingCriteria, SortOrder order, Integer startIndex, Integer pageSize) {
        return null;
    }

    @Override
    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return Collections.emptyList();
    }

    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return null;
    }

    @Override
    public void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds,List<LocationPagePair> locationPagePairList,
                                                      Map<String, Boolean> pagePermissionMap) {
        List<BusinessTiktokAccounts> tiktokAccountsList = businessTiktokAccountsRepository.findByBusinessIdIn(businessIds);
        if (CollectionUtils.isNotEmpty(tiktokAccountsList)) {
            tiktokAccountsList.forEach(tiktokAccounts ->
                    updateLocationPagePairAndPagePermissionMap(locationPagePairList,
                            pagePermissionMap, tiktokAccounts.getProfileId(), tiktokAccounts.getBusinessId(), tiktokAccounts.getCanPost()));
        }
    }

    @Override
    public void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList,
                                                   Map<String, Boolean> pagePermissionMap) {
        List<BusinessTiktokAccounts> tiktokAccountsList = businessTiktokAccountsRepository.findByProfileIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(tiktokAccountsList)) {
            tiktokAccountsList.forEach(tiktokAccounts ->
                    updateLocationPagePairAndPagePermissionMap(locationPagePairList,
                            pagePermissionMap, tiktokAccounts.getProfileId(), tiktokAccounts.getBusinessId(), tiktokAccounts.getCanPost()));
        }
    }

    private void updateLocationPagePairAndPagePermissionMap(List<LocationPagePair> locations, Map<String, Boolean> pagePermissionMap,
                                                            String pageId, Integer businessId, Integer canPost) {
        locations.add(new LocationPagePair(pageId, businessId));
        if (pagePermissionMap != null) {
            pagePermissionMap.put(pageId, Objects.equals(canPost,1));
        }
    }

    @Override
    public List<String> getAllPageIds(int page, int size, Long enterpriseId) {
        return Objects.nonNull(enterpriseId)? tiktokAccountsRepository.findDistinctProfileIdsByEnterpriseId(enterpriseId):
                tiktokAccountsRepository.findProfileIdsWithPagination(new PageRequest(page,size));
    }

    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {

    }

    @Override
    public Map<String, PageInfoDto> getPageInfoDto(List<String> pageIds) {
        return Collections.emptyMap();
    }
}
