package com.birdeye.social.service;

import com.birdeye.social.model.GlobalFilterCriteriaSchedulePostMessage;
import com.birdeye.social.model.SocialPostEsSyncRequest;
import com.birdeye.social.model.SocialSchedulePostResponse;
import com.birdeye.social.model.es.SocialPostsESSyncRequest;

import java.util.Collection;

public interface SocialPostCalendarService {
    SocialSchedulePostResponse getAllScheduledPostsV2(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListViewRequest, String source) throws Exception;

    void syncRecordsOnEsBySocialPostIds(Collection<Integer> socialPostIds);

    SocialSchedulePostResponse getAllPosts(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListViewRequest, String source,String timezone) throws Exception;

    SocialSchedulePostResponse getAllESScheduledPosts(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListViewRequest, String source,
                                                      String timezone) throws Exception;

    SocialSchedulePostResponse getAllESScheduledPostsV2(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListViewRequest, String source,
                                                      String timezone) throws Exception;

    SocialSchedulePostResponse getAllScheduledPosts(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListViewRequest) throws Exception;

    Integer socialPostsESSyncRequest(SocialPostsESSyncRequest socialPostsESSyncRequest);

    SocialSchedulePostResponse getAllScheduledPostsForResellerV2(GlobalFilterCriteriaSchedulePostMessage filter);

    void saveSuspendedDeletedPost(SocialPostEsSyncRequest socialPostsESSyncRequest) throws Exception;
}
