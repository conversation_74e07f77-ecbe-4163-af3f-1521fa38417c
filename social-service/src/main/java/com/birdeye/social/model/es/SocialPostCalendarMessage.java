package com.birdeye.social.model.es;

import com.birdeye.social.insights.ES.CalendarViewPagePostInsightsData;
import com.birdeye.social.model.*;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString
public class SocialPostCalendarMessage {

    private Integer id;
    private String postText;
    private Boolean aiPost;
    private List<MediaData> images;
    private List<String> compressedMediaSequence;
    private List<Integer> compressedImages;
    private List<MediaData> videos;
    private List<String> videoThumbnails;
    private String linkPreviewUrl;
    private Date publishDate;
    private String publishedBy;
    private String datePublish;
    private Integer scheduleInfoId;
    private List<String> postingSites;
    private Integer isPublished;
    private List<String> mediaSequence;
    private List<MentionData> mentions;
    private SocialPostPermissionStatusResponse permissionStatus;
    private List<CalendarViewPagePostInsightsData> postInsights;
    private List<String> incompleteChannel;
    private Boolean hasPostFailed = false;
    private Integer failedChannelCount;
    private Integer failedPageCount;
    private List<String> failedSites;
    private String type;
    private GoogleOfferDetails gmbOfferDetails;
    private Boolean isExpired;
    private Integer duplicatedCount;
    private Boolean isQuotedTweet;
    private List<SocialTagBasicDetail> tags;
    private Boolean isCreator = false;
    private Boolean isApprover = false;
    private Integer approvalWorkflowId;
    private String approvalStatus;
    private String approvalUUId;
    private Integer approvalRequestId;
    private String conversationId;
    private Integer referenceStepId;
    private String endDate;
    private String applePublishStatus;
    private Integer createdBy;
    private List<Integer> imageIds;
    private List<Integer> videoIds;
    private Integer enterpriseId;
    private List<String> approvalUserIds;
    private Integer quotedPostId;
    private String quotedPostIdNew;
    private String quotedPostUrl;
    private Integer sourceId;
    private List<String> pageIds;
    private List<String> pageIdsNew;
    private String quotedTweetSource;
    private String createdByName;
    private String postGroupDetails;
    private List<String> levelNames;
    private String levelAlias;
    private Integer levelId;
    private String postMethod;
    private SocialPostSchedulerMetadata postMetaData;
    private String recordCreatedDate;
    private String recordUpdatedDate;
    private List<Integer> publishInfoIds;
    private List<String> failedPageIds;
    private Long publishTimeStamp;
    private String postHeader;
    private Integer aiPostId;
    private boolean aiGenerated;
    private String aiReferencePostId;
    private String aiReason;

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof SocialPostCalendarMessage)) {
            return false;
        }
        SocialPostCalendarMessage s = (SocialPostCalendarMessage) o;
        return this.id.equals(s.id);
    }

}