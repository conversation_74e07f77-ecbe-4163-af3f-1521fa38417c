package com.birdeye.social.model;

import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.model.es.SocialPostCalendarMessage;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class CalendarPostDataDTO {

    private Map<Integer, List<String>> hasIncompleteAccessMap;
    private BusinessLiteDTO businessLiteDTO;
    private Map<Integer, BusinessCoreUser> userDetailMap;
    private Map<Integer, SocialPostsAssets> postAssetsMap;
    private List<SocialPostCalendarMessage> applePostList;
    private Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap;

}
