package com.birdeye.social.insights.constants;

public enum ElasticConstants {

    FACEBOOK_PAGE_INSIGHTS("facebook_page_insight"),
    TIKTOK_PAGE_INSIGHTS("tiktok_page_insight"),
    TWITTER_PAGE_INSIGHTS("twitter_page_insight"),
    INSTAGRAM_PAGE_INSIGHTS("instagram_page_insight"),
    TWITTER_POST_INSIGHTS("twitter_post_insight"),
    GMB_POST_INSIGHTS("gmb_post_insight"),
    LINKEDIN_PAGE_INSIGHTS("linkedin_page_insight"),
    PAGE_INSIGHTS("page_insight"),
    POST_INSIGHTS("post_insight"),
    LINKEDIN_POST_INSIGHTS("linkedin_post_insight"),
    GMB_REPORT_PAGE_INSIGHTS("gmb_report_page_insight"),
    SOCIAL_POST_APPROVAL("social_post_approval"),
    SOCIAL_APPROVAL_STATS("social_approval_stats"),
    SOCIAL_ENGAGE_V2_FEED("social_engage_feed"),
    SOCIAL_ENGAGE_STATS("social_engage_stats"),
    MENTION("mention"),
    YOUTUBE_PAGE_INSIGHTS("youtube_page_insight"),
    COMPETITOR_POSTS("competitor_posts"),
    CALENDAR_DATA("calendar_data"),

    CALENDAR_DATA_DEV("calendar_data_dev"),

    COMPETITOR_PAGE_SCANNED("competitor_page_scanned"),

    COMPETITOR_FB_PROFILE_INSIGHTS("social_competitor_fb_profile_insights"),

    COMPETITOR_INSTA_PROFILE_INSIGHTS("social_competitor_insta_profile_insights"),

    COMPETITOR_TWITTER_PROFILE_INSIGHTS("social_competitor_twitter_profile_insights"),
    SUSPENDED_DELETED_POSTS("suspended_deleted_posts");

    final String name;

    public String getName() {
        return name;
    }

    ElasticConstants(String name) {
        this.name = name;
    }
}
